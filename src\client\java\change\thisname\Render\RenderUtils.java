package change.thisname.Render;

import net.minecraft.client.gui.DrawContext;

public class RenderUtils {
    
    /**
     * Draws a rounded rectangle with the specified parameters
     */
    public static void drawRoundedRect(DrawContext context, int x, int y, int width, int height, int radius, int color) {
        if (radius <= 0) {
            context.fill(x, y, x + width, y + height, color);
            return;
        }
        
        // Clamp radius to prevent weird shapes
        radius = Math.min(radius, Math.min(width / 2, height / 2));
        
        // Draw the main rectangles
        context.fill(x + radius, y, x + width - radius, y + height, color); // Center
        context.fill(x, y + radius, x + radius, y + height - radius, color); // Left
        context.fill(x + width - radius, y + radius, x + width, y + height - radius, color); // Right
        
        // Draw the corners using circles
        drawCircleCorner(context, x + radius, y + radius, radius, color, 2); // Top-left
        drawCircleCorner(context, x + width - radius, y + radius, radius, color, 1); // Top-right
        drawCircleCorner(context, x + radius, y + height - radius, radius, color, 3); // Bottom-left
        drawCircleCorner(context, x + width - radius, y + height - radius, radius, color, 0); // Bottom-right
    }
    
    /**
     * Draws a rounded rectangle border
     */
    public static void drawRoundedBorder(DrawContext context, int x, int y, int width, int height, int radius, int borderWidth, int color) {
        if (radius <= 0) {
            context.drawBorder(x, y, width, height, color);
            return;
        }
        
        radius = Math.min(radius, Math.min(width / 2, height / 2));
        
        // Draw border lines
        context.fill(x + radius, y, x + width - radius, y + borderWidth, color); // Top
        context.fill(x + radius, y + height - borderWidth, x + width - radius, y + height, color); // Bottom
        context.fill(x, y + radius, x + borderWidth, y + height - radius, color); // Left
        context.fill(x + width - borderWidth, y + radius, x + width, y + height - radius, color); // Right
        
        // Draw corner borders
        drawCircleCornerBorder(context, x + radius, y + radius, radius, borderWidth, color, 2); // Top-left
        drawCircleCornerBorder(context, x + width - radius, y + radius, radius, borderWidth, color, 1); // Top-right
        drawCircleCornerBorder(context, x + radius, y + height - radius, radius, borderWidth, color, 3); // Bottom-left
        drawCircleCornerBorder(context, x + width - radius, y + height - radius, radius, borderWidth, color, 0); // Bottom-right
    }
    
    private static void drawCircleCorner(DrawContext context, int centerX, int centerY, int radius, int color, int quadrant) {
        for (int x = -radius; x <= radius; x++) {
            for (int y = -radius; y <= radius; y++) {
                if (x * x + y * y <= radius * radius) {
                    boolean shouldDraw = false;
                    switch (quadrant) {
                        case 0: shouldDraw = x >= 0 && y >= 0; break; // Bottom-right
                        case 1: shouldDraw = x <= 0 && y >= 0; break; // Bottom-left -> Top-right
                        case 2: shouldDraw = x <= 0 && y <= 0; break; // Top-left
                        case 3: shouldDraw = x >= 0 && y <= 0; break; // Top-right -> Bottom-left
                    }
                    if (shouldDraw) {
                        context.fill(centerX + x, centerY + y, centerX + x + 1, centerY + y + 1, color);
                    }
                }
            }
        }
    }
    
    private static void drawCircleCornerBorder(DrawContext context, int centerX, int centerY, int radius, int borderWidth, int color, int quadrant) {
        for (int x = -radius; x <= radius; x++) {
            for (int y = -radius; y <= radius; y++) {
                int distSq = x * x + y * y;
                int outerRadiusSq = radius * radius;
                int innerRadiusSq = (radius - borderWidth) * (radius - borderWidth);
                
                if (distSq <= outerRadiusSq && distSq >= innerRadiusSq) {
                    boolean shouldDraw = false;
                    switch (quadrant) {
                        case 0: shouldDraw = x >= 0 && y >= 0; break; // Bottom-right
                        case 1: shouldDraw = x <= 0 && y >= 0; break; // Bottom-left -> Top-right
                        case 2: shouldDraw = x <= 0 && y <= 0; break; // Top-left
                        case 3: shouldDraw = x >= 0 && y <= 0; break; // Top-right -> Bottom-left
                    }
                    if (shouldDraw) {
                        context.fill(centerX + x, centerY + y, centerX + x + 1, centerY + y + 1, color);
                    }
                }
            }
        }
    }
    
    /**
     * Draws a gradient rounded rectangle
     */
    public static void drawGradientRoundedRect(DrawContext context, int x, int y, int width, int height, int radius, int colorTop, int colorBottom) {
        if (radius <= 0) {
            drawGradientRect(context, x, y, width, height, colorTop, colorBottom);
            return;
        }
        
        radius = Math.min(radius, Math.min(width / 2, height / 2));
        
        // For simplicity, we'll approximate the gradient with multiple horizontal lines
        for (int i = 0; i < height; i++) {
            float progress = (float) i / (height - 1);
            int currentColor = interpolateColor(colorTop, colorBottom, progress);
            
            int lineY = y + i;
            int lineStartX = x;
            int lineEndX = x + width;
            
            // Adjust for rounded corners
            if (i < radius) {
                // Top corners
                int offset = (int) Math.sqrt(radius * radius - (radius - i) * (radius - i));
                lineStartX = x + radius - offset;
                lineEndX = x + width - radius + offset;
            } else if (i >= height - radius) {
                // Bottom corners
                int offset = (int) Math.sqrt(radius * radius - (i - (height - radius)) * (i - (height - radius)));
                lineStartX = x + radius - offset;
                lineEndX = x + width - radius + offset;
            }
            
            if (lineEndX > lineStartX) {
                context.fill(lineStartX, lineY, lineEndX, lineY + 1, currentColor);
            }
        }
    }
    
    private static void drawGradientRect(DrawContext context, int x, int y, int width, int height, int colorTop, int colorBottom) {
        for (int i = 0; i < height; i++) {
            float progress = (float) i / (height - 1);
            int currentColor = interpolateColor(colorTop, colorBottom, progress);
            context.fill(x, y + i, x + width, y + i + 1, currentColor);
        }
    }
    
    private static int interpolateColor(int color1, int color2, float progress) {
        int a1 = (color1 >> 24) & 0xFF;
        int r1 = (color1 >> 16) & 0xFF;
        int g1 = (color1 >> 8) & 0xFF;
        int b1 = color1 & 0xFF;
        
        int a2 = (color2 >> 24) & 0xFF;
        int r2 = (color2 >> 16) & 0xFF;
        int g2 = (color2 >> 8) & 0xFF;
        int b2 = color2 & 0xFF;
        
        int a = (int) (a1 + (a2 - a1) * progress);
        int r = (int) (r1 + (r2 - r1) * progress);
        int g = (int) (g1 + (g2 - g1) * progress);
        int b = (int) (b1 + (b2 - b1) * progress);
        
        return (a << 24) | (r << 16) | (g << 8) | b;
    }
}
