package change.thisname.Render.GUI;

import net.minecraft.client.gui.screen.Screen;
import net.minecraft.text.Text;
import net.minecraft.client.gui.DrawContext;
import java.util.Arrays;
import java.util.List;
import java.util.HashMap;
import java.util.Map;
import java.util.HashSet;
import java.util.Set;
import java.util.LinkedHashMap;
import java.util.stream.Collectors;

// Import all your specific module classes
import change.thisname.Modules.BaseModule;
import change.thisname.Modules.AimAssist;
import change.thisname.Modules.AutoWalk;
import change.thisname.Modules.Timer;
import change.thisname.Modules.ESP;
import change.thisname.Config.ConfigManager;
import change.thisname.Render.RenderUtils;
// Add other module imports here as you create their classes
// import change.thisname.Modules.AutoCrystal;
// import change.thisname.Modules.Flight;
// etc.

public class ModuleScreen extends Screen {

    // Store actual instances of modules, not just anonymous classes
    public static final Map<String, List<BaseModule>> CATEGORIES = new LinkedHashMap<>();
    static {
        CATEGORIES.put("Combat", Arrays.asList(
                new AimAssist(),
                // For modules without specific classes, keep anonymous BaseModule instances
                new BaseModule("Anchor Macro") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("AutoCrystal") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("AutoStun") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("Backtrack") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("Criticals") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("Hit Crystal") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("HitSelection") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("Hitboxes") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("MaceSwap") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("Piercing") { @Override public void onEnable() {} @Override public void onDisable() {} }, // CORRECTED LINE
                new BaseModule("SprintReset") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("TickBase") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("TriggerBot") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("Velocity") { @Override public void onEnable() {} @Override public void onDisable() {} }
        ));

        CATEGORIES.put("Movement", Arrays.asList(
                new AutoWalk(),
                new BaseModule("Elytra+") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("Flight") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("Jesus") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("NoFall") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("Phase") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("SafeWalk") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("Spider") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("Sprint") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("Step") { @Override public void onEnable() {} @Override public void onDisable() {} }
        ));

        CATEGORIES.put("Player", Arrays.asList(
                new BaseModule("AntiHunger") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("AutoEat") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("AutoRespawn") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("AutoTool") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("FastBreak") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("FastPlace") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("Freecam") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("NoRotate") { @Override public void onEnable() {} @Override public void onDisable() {} }
        ));

        CATEGORIES.put("Render", Arrays.asList(
                new ESP(),
                new BaseModule("Fullbright") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("NoRender") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("StorageESP") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("Tracers") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("WallHack") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("Xray") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("Zoom") { @Override public void onEnable() {} @Override public void onDisable() {} }
        ));

        CATEGORIES.put("World", Arrays.asList(
                new BaseModule("AutoMine") { @Override public void onEnable() {} @Override public void onDisable() {} }, // CORRECTED LINE
                new BaseModule("Baritone") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("BuildAssist") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("Nuker") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new BaseModule("Scaffold") { @Override public void onEnable() {} @Override public void onDisable() {} },
                new Timer()
        ));
    }


    private static final int RECT_WIDTH = 120;
    private static final int RECT_HEIGHT = 20;
    private static final int RECT_MARGIN = 2;
    private static final int CATEGORY_ROW_SPACING = 20; // Vertical space between category rows
    private static final int CORNER_RADIUS = 4; // Rounded corner radius

    private Set<String> expandedCategories = new HashSet<>();

    private static class ModuleAnimationState {
        float currentY;
        float targetY;
        float velocityY;
        float startDelay;
        boolean hasStarted;
        boolean isSettled;
        float scale;
        float rotation;
        float alpha;

        ModuleAnimationState(float startY, float targetY, float delay) {
            this.currentY = startY;
            this.targetY = targetY;
            this.velocityY = 0;
            this.startDelay = delay;
            this.hasStarted = false;
            this.isSettled = false;
            this.scale = 0.8f;
            this.rotation = (float)(Math.random() - 0.5) * 20f;
            this.alpha = 0;
        }

        void updateTargetY(float newTargetY) {
            if (this.targetY != newTargetY && this.isSettled) {
                // If settled, reset animation slightly to smoothly move to new target
                this.isSettled = false;
                this.velocityY = 0; // Or a small push towards new target
                // If moving far, might want to set currentY closer to targetY to avoid large jumps
                this.currentY = Math.max(this.currentY, newTargetY - RECT_HEIGHT); // Don't jump too far up
            }
            this.targetY = newTargetY;
        }
    }

    private static class SettingsAnimationState {
        float currentY;
        float targetY;
        float velocityY;
        boolean isSettled;
        float scale;
        float alpha;
        long startTime; // To manage delay and duration

        SettingsAnimationState(float startY, float targetY) {
            this.currentY = startY;
            this.targetY = targetY;
            this.velocityY = 0;
            this.isSettled = false;
            this.scale = 0.5f;
            this.alpha = 0f;
            this.startTime = System.currentTimeMillis();
        }

        void updateTargetY(float newTargetY) {
            this.targetY = newTargetY;
            this.velocityY = 0; // Ensure no bounce when target changes
        }
    }

    private Map<String, Integer> categoryX = new HashMap<>();
    private Map<String, Integer> categoryY = new HashMap<>();

    // Map to hold animation states for all modules, even if not currently visible
    private Map<BaseModule, ModuleAnimationState> allModuleAnimations = new HashMap<>();
    // Map to track when a category was last toggled, used for module entry animation delay
    private Map<String, Long> categoryToggleTime = new HashMap<>();

    private boolean isDragging = false;
    private String draggedCategory = null;
    private double dragStartX, dragStartY;
    private int dragStartCategoryX, dragStartCategoryY;

    private BaseModule settingsModule = null;
    private SettingsAnimationState currentSettingsAnimation = null;
    private int settingsX, settingsY; // These are where the popup *originated* from, not its animated position

    private static final float GRAVITY = 2.0f; // Increased for faster drop
    private static final float BOUNCE_DAMPING = 0.8f; // Increased for less bouncing
    private static final float SETTLE_THRESHOLD = 1.0f; // Increased for quicker settling
    private static final float MODULE_DELAY = 30f; // Reduced for faster cascading
    private static final float SCALE_SPEED = 0.8f; // Increased for faster scaling
    private static final float ROTATION_SPEED = 0.3f; // Increased for faster rotation
    private static final float SETTINGS_ANIM_SPEED = 0.25f;
    private static final long SETTINGS_ANIM_DURATION_MS = 40; // How long settings popup animation lasts (reduced for faster animation)

    // Screen animation constants
    private static final float SCREEN_ANIM_SPEED = 0.15f;
    private static final long SCREEN_ANIM_DURATION_MS = 300; // Screen opening/closing animation duration

    // Screen animation state
    private float screenScale = 0.0f;
    private float screenAlpha = 0.0f;
    private boolean isOpening = true;
    private boolean isClosing = false;
    private long screenAnimStartTime = 0;
    private boolean firstLaunch = true;

    public ModuleScreen(Text title) {
        super(title);

        // Load saved configurations
        loadSavedData();

        initializeCategoryPositions();
        // Initialize all module animation states once at startup
        CATEGORIES.values().forEach(modules -> modules.forEach(module -> {
            allModuleAnimations.put(module, new ModuleAnimationState(this.height + 50, -100, 0)); // Off-screen initially
        }));

        // Initialize screen animation
        screenAnimStartTime = System.currentTimeMillis();
        isOpening = true;
        screenScale = 0.0f;
        screenAlpha = 0.0f;
    }

    private void loadSavedData() {
        // Load module configurations
        ConfigManager.loadModuleConfigs();

        // Load UI state
        ConfigManager.UIState uiState = ConfigManager.loadUIState();
        expandedCategories.addAll(uiState.expandedCategories);
        firstLaunch = uiState.firstLaunch;

        // Load category positions
        Map<String, ConfigManager.CategoryPosition> positions = ConfigManager.loadCategoryPositions();
        for (Map.Entry<String, ConfigManager.CategoryPosition> entry : positions.entrySet()) {
            categoryX.put(entry.getKey(), entry.getValue().x);
            categoryY.put(entry.getKey(), entry.getValue().y);
        }
    }

    private void saveAllData() {
        // Save category positions
        ConfigManager.saveCategoryPositions(categoryX, categoryY);

        // Save module configurations
        ConfigManager.saveModuleConfigs();

        // Save UI state
        ConfigManager.saveUIState(expandedCategories, false);
    }

    private void initializeCategoryPositions() {
        if (categoryX.isEmpty() || this.width > 0) { // Reinitialize if width changes or first run
            int startY = this.height > 0 ? this.height / 10 : 100;
            int totalCategories = CATEGORIES.size();
            // Calculate spacing based on available width and 3 categories per row
            int spacing = this.width > 0 ? (this.width - (RECT_WIDTH * 3)) / 4 : 50;
            if (spacing < 20) spacing = 20;

            int currentX = spacing;
            int currentY = startY;
            int categoryIndex = 0;
            for (String category : CATEGORIES.keySet()) {
                if (!categoryX.containsKey(category)) { // Only set initial position if not already set (e.g., from save)
                    categoryX.put(category, currentX);
                    categoryY.put(category, currentY);
                }


                if ((categoryIndex + 1) % 3 == 0) { // Assuming 3 categories per row
                    currentX = spacing;
                    currentY += RECT_HEIGHT + CATEGORY_ROW_SPACING;
                } else {
                    currentX += RECT_WIDTH + spacing;
                }
                categoryIndex++;
            }
        }
    }

    // This method now only updates initial 'targetY' for modules, not creates new states
    private void activateModuleAnimationsForCategory(String category) {
        if (!expandedCategories.contains(category)) return;

        List<BaseModule> modules = CATEGORIES.get(category);
        long currentTime = System.currentTimeMillis();
        categoryToggleTime.put(category, currentTime); // Mark toggle time for animation delays

        for (int i = 0; i < modules.size(); i++) {
            BaseModule module = modules.get(i);
            ModuleAnimationState anim = allModuleAnimations.get(module);
            if (anim != null) {
                // Set the initial state for appearing modules
                anim.currentY = categoryY.get(category) + RECT_HEIGHT / 2; // Start from center of category title
                anim.velocityY = 0;
                anim.hasStarted = false; // Reset to trigger delay
                anim.isSettled = false;
                anim.scale = 0.8f;
                anim.rotation = (float)(Math.random() - 0.5) * 20f;
                anim.alpha = 0;
                anim.startDelay = i * (MODULE_DELAY / 5.0f); // Apply shorter delay for faster cascading effect
            }
        }
        recalculateModulePositions(category); // Calculate exact targetY for all modules
    }

    // New method to dynamically calculate target Y for modules within a category
    private void recalculateModulePositions(String category) {
        int currentCategoryX = categoryX.get(category);
        int currentCategoryY = categoryY.get(category);

        int moduleBlockStartY = currentCategoryY + RECT_HEIGHT + RECT_MARGIN;
        int currentModuleY = moduleBlockStartY;

        List<BaseModule> modules = CATEGORIES.get(category);
        for (BaseModule module : modules) {
            ModuleAnimationState moduleAnim = allModuleAnimations.get(module);
            if (moduleAnim != null) {
                moduleAnim.updateTargetY(currentModuleY); // Update target position
            }

            // If this module has its settings open, make space for it
            if (module == settingsModule && currentSettingsAnimation != null && expandedCategories.contains(category)) {
                int settingsPopupHeight = 35 + settingsModule.getSettings().size() * 15;
                currentModuleY += settingsPopupHeight + RECT_MARGIN; // Add space for settings
            }
            currentModuleY += RECT_HEIGHT + RECT_MARGIN; // Space for the module itself
        }

        // Also update settings popup target Y if it's active
        if (settingsModule != null && currentSettingsAnimation != null && CATEGORIES.get(category).contains(settingsModule)) {
            int moduleIndex = CATEGORIES.get(category).indexOf(settingsModule);
            if (moduleIndex != -1) {
                float moduleTargetY = allModuleAnimations.get(settingsModule).targetY;
                float newSettingsTargetY = moduleTargetY + RECT_HEIGHT + RECT_MARGIN;
                currentSettingsAnimation.updateTargetY(newSettingsTargetY);
            }
        }
    }


    @Override
    public boolean shouldPause() {
        return false;
    }

    @Override
    public void renderBackground(DrawContext context, int mouseX, int mouseY, float delta) {
        // No custom background rendering
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        this.renderBackground(context, mouseX, mouseY, delta);

        initializeCategoryPositions(); // Ensure positions are set if not already

        updateScreenAnimation(delta);

        // Apply screen animation transformations
        context.getMatrices().push();
        context.getMatrices().translate(this.width / 2.0f, this.height / 2.0f, 0);
        context.getMatrices().scale(screenScale, screenScale, 1.0f);
        context.getMatrices().translate(-this.width / 2.0f, -this.height / 2.0f, 0);

        // Apply alpha for fade effect
        int titleAlpha = (int)(screenAlpha * 255) << 24 | 0xFFFFFF;
        context.drawCenteredTextWithShadow(this.textRenderer, this.title, this.width / 2, 20, titleAlpha);

        updateModuleAnimations(delta);
        updateSettingsAnimation(delta);
        renderCategories(context, mouseX, mouseY);
        renderSettingsPopup(context);

        context.getMatrices().pop();
    }

    private void updateScreenAnimation(float delta) {
        long currentTime = System.currentTimeMillis();
        long elapsed = currentTime - screenAnimStartTime;
        float progress = Math.min(1.0f, (float) elapsed / SCREEN_ANIM_DURATION_MS);

        if (isOpening) {
            // Smooth easing function for opening
            float easedProgress = 1.0f - (float) Math.pow(1.0f - progress, 3);
            screenScale = easedProgress;
            screenAlpha = easedProgress;

            if (progress >= 1.0f) {
                isOpening = false;
                screenScale = 1.0f;
                screenAlpha = 1.0f;
            }
        } else if (isClosing) {
            // Smooth easing function for closing
            float easedProgress = (float) Math.pow(1.0f - progress, 3);
            screenScale = easedProgress;
            screenAlpha = easedProgress;

            if (progress >= 1.0f) {
                // Close the screen
                if (this.client != null) {
                    this.client.setScreen(null);
                }
            }
        }
    }

    private void updateModuleAnimations(float delta) {
        for (String category : expandedCategories) {
            long toggleTime = categoryToggleTime.getOrDefault(category, 0L);
            long currentTime = System.currentTimeMillis();

            List<BaseModule> modules = CATEGORIES.get(category);
            for (BaseModule module : modules) {
                ModuleAnimationState anim = allModuleAnimations.get(module);
                if (anim == null) continue; // Should not happen if initialized correctly

                float elapsedTime = currentTime - toggleTime;

                // Only start animation if category is expanded and delay has passed
                if (!anim.hasStarted && elapsedTime >= anim.startDelay) {
                    anim.hasStarted = true;
                    anim.alpha = 1.0f;
                }

                if (!anim.hasStarted) continue;

                // Physics simulation
                if (!anim.isSettled) {
                    anim.velocityY += GRAVITY * delta;
                    anim.currentY += anim.velocityY * delta;

                    if (anim.currentY >= anim.targetY) {
                        anim.currentY = anim.targetY;
                        anim.velocityY = -anim.velocityY * BOUNCE_DAMPING;

                        anim.rotation += (float)(Math.random() - 0.5) * 15f;

                        if (Math.abs(anim.velocityY) < SETTLE_THRESHOLD) {
                            anim.isSettled = true;
                            anim.velocityY = 0;
                            anim.currentY = anim.targetY;
                        }
                    }
                    anim.rotation += anim.velocityY * ROTATION_SPEED * delta;
                }

                if (anim.isSettled && anim.scale < 1.0f) {
                    anim.scale += SCALE_SPEED * delta;
                    if (anim.scale >= 1.0f) {
                        anim.scale = 1.0f;
                    }
                }

                if (anim.isSettled) {
                    anim.rotation *= 0.85f; // Faster rotation damping
                    if (Math.abs(anim.rotation) < 0.1f) {
                        anim.rotation = 0;
                    }
                }
            }
        }

        // For modules in collapsed categories, move them off-screen
        Set<BaseModule> visibleModules = expandedCategories.stream()
                .flatMap(category -> CATEGORIES.get(category).stream())
                .collect(Collectors.toSet());

        for (Map.Entry<BaseModule, ModuleAnimationState> entry : allModuleAnimations.entrySet()) {
            BaseModule module = entry.getKey();
            ModuleAnimationState anim = entry.getValue();

            if (!visibleModules.contains(module)) {
                // If module is not in an expanded category, move it off-screen
                anim.updateTargetY(this.height + 50); // Target off-screen below
            } else if (settingsModule != null && module != settingsModule) {
                // If settings are open and this is not the settings module, push it off-screen
                anim.updateTargetY(this.height + 50); // Target off-screen below
                anim.isSettled = false; // Force re-animation if it was settled
                anim.hasStarted = true; // Still animate off-screen
                anim.alpha -= SETTINGS_ANIM_SPEED * delta; // Fade out
                if (anim.alpha < 0) anim.alpha = 0;
            }
        }
    }

    private void updateSettingsAnimation(float delta) {
        if (settingsModule != null && currentSettingsAnimation != null) {
            SettingsAnimationState anim = currentSettingsAnimation;

            // Fade in and grow
            if (anim.scale < 1.0f) {
                anim.scale += SETTINGS_ANIM_SPEED * delta * 2; // Faster growth
                if (anim.scale > 1.0f) anim.scale = 1.0f;
            }
            if (anim.alpha < 1.0f) {
                anim.alpha += SETTINGS_ANIM_SPEED * delta * 2; // Faster fade in
                if (anim.alpha > 1.0f) anim.alpha = 1.0f;
            }

            // Smoothly move towards targetY
            if (Math.abs(anim.currentY - anim.targetY) > SETTLE_THRESHOLD) {
                anim.currentY += (anim.targetY - anim.currentY) * SETTINGS_ANIM_SPEED * delta * 2;
            } else {
                anim.currentY = anim.targetY;
                anim.isSettled = true;
            }
        } else if (currentSettingsAnimation != null) {
            // If settingsModule is null but animation state still exists (means closing)
            SettingsAnimationState anim = currentSettingsAnimation;
            long elapsedTime = System.currentTimeMillis() - anim.startTime;

            if (elapsedTime < SETTINGS_ANIM_DURATION_MS) {
                // Animate closing: fade out and shrink
                float progress = 1 - (float) elapsedTime / SETTINGS_ANIM_DURATION_MS;
                anim.alpha = progress;
                anim.scale = 0.5f + (0.5f * progress); // Shrink back to 0.5
                if (anim.alpha < 0.1f) anim.alpha = 0; // Snap to 0 for invisibility
                if (anim.scale < 0.55f) anim.scale = 0.5f; // Snap to initial size
                // No need for physics for closing, just a clean fade/shrink
            } else {
                currentSettingsAnimation = null; // Animation finished, clear state
            }
        }
    }

    private void renderCategories(DrawContext context, int mouseX, int mouseY) {
        String draggedCat = isDragging ? draggedCategory : null;

        for (String category : CATEGORIES.keySet()) {
            if (category.equals(draggedCat)) continue;
            renderCategory(context, category, mouseX, mouseY, false);
        }

        if (draggedCat != null) {
            renderCategory(context, draggedCat, mouseX, mouseY, true);
        }
    }

    private void renderCategory(DrawContext context, String category, int mouseX, int mouseY, boolean isDraggedCategory) {
        int startX, startY;

        if (isDraggedCategory) {
            // Calculate current drag position
            startX = dragStartCategoryX + (int)(mouseX - dragStartX);
            startY = dragStartCategoryY + (int)(mouseY - dragStartY);

            // Clamp to screen bounds
            startX = Math.max(0, Math.min(this.width - RECT_WIDTH, startX));
            startY = Math.max(0, Math.min(this.height - RECT_HEIGHT, startY));

            // Update stored position for modules to follow immediately
            categoryX.put(category, startX);
            categoryY.put(category, startY);

            // Recalculate module positions instantly while dragging
            if (expandedCategories.contains(category)) {
                recalculateModulePositions(category);
            }

        } else {
            startX = categoryX.get(category);
            startY = categoryY.get(category);
        }

        boolean isExpanded = expandedCategories.contains(category);

        boolean isHovering = mouseX >= startX && mouseX <= startX + RECT_WIDTH &&
                           mouseY >= startY && mouseY <= startY + RECT_HEIGHT;

        int categoryColor;
        if (isDraggedCategory) {
            categoryColor = 0xFF4A4A4A; // Darker when dragged
        } else if (isHovering) {
            categoryColor = 0xFF353535; // Hover color
        } else {
            categoryColor = 0xFF2A2A2A; // Normal color
        }

        // Apply screen alpha to colors
        int alpha = (int)(screenAlpha * 255);
        int finalCategoryColor = (alpha << 24) | (categoryColor & 0xFFFFFF);
        int borderColor = (alpha << 24) | 0x404040;
        int textColor = (alpha << 24) | 0xFFFFFF;
        int menuIconColor = (alpha << 24) | 0x888888;

        RenderUtils.drawRoundedRect(context, startX, startY, RECT_WIDTH, RECT_HEIGHT, CORNER_RADIUS, finalCategoryColor);
        RenderUtils.drawRoundedBorder(context, startX, startY, RECT_WIDTH, RECT_HEIGHT, CORNER_RADIUS, 1, borderColor);

        // Add subtle inner highlight for hover/drag
        if (isHovering || isDraggedCategory) {
            context.fill(startX + 1, startY + 1, startX + RECT_WIDTH - 1, startY + 2,
                        (alpha << 24) | 0x404040);
        }

        context.drawTextWithShadow(this.textRenderer, "≡",
            startX + RECT_WIDTH - 25, startY + (RECT_HEIGHT - 8) / 2, menuIconColor);

        String arrow = isExpanded ? "▼" : "▶";
        int arrowColor = isExpanded ? ((alpha << 24) | 0x4A90E2) : textColor;

        context.drawTextWithShadow(this.textRenderer, category,
            startX + 5, startY + (RECT_HEIGHT - 8) / 2, textColor);
        context.drawTextWithShadow(this.textRenderer, arrow,
            startX + RECT_WIDTH - 40, startY + (RECT_HEIGHT - 8) / 2, arrowColor);

        if (isExpanded) {
            renderModules(context, category, startX, startY, mouseX, mouseY);
        }
    }

    private void renderModules(DrawContext context, String category, int categoryRenderX, int categoryRenderY, int mouseX, int mouseY) {
        List<BaseModule> modules = CATEGORIES.get(category);

        for (int i = 0; i < modules.size(); i++) {
            BaseModule module = modules.get(i);
            ModuleAnimationState anim = allModuleAnimations.get(module);

            if (anim == null || !anim.hasStarted || anim.alpha <= 0) continue; // Only render if started and visible

            // If settings are open, make other modules invisible
            if (settingsModule != null && module != settingsModule) {
                anim.alpha = 0; // Make invisible
                continue;
            }

            int moduleX = categoryRenderX + 5; // Modules follow category's X position
            int moduleY = (int)anim.currentY;

            context.getMatrices().push();

            float centerX = moduleX + (RECT_WIDTH - 5) / 2f;
            float centerY = moduleY + RECT_HEIGHT / 2f;

            context.getMatrices().translate(centerX, centerY, 0);
            context.getMatrices().scale(anim.scale, anim.scale, 1.0f);
            context.getMatrices().multiply(net.minecraft.util.math.RotationAxis.POSITIVE_Z.rotationDegrees(anim.rotation));
            context.getMatrices().translate(-(RECT_WIDTH - 5) / 2f, -RECT_HEIGHT / 2f, 0);

            // Calculate hover for untransformed rectangle, simpler hit detection
            boolean moduleHover = mouseX >= moduleX && mouseX <= moduleX + RECT_WIDTH - 5 &&
                                mouseY >= moduleY && mouseY <= moduleY + RECT_HEIGHT && anim.isSettled;

            int alpha = (int)(anim.alpha * screenAlpha * 255) << 24;

            // Enhanced module colors
            int moduleColor;
            if (module.isEnabled()) {
                moduleColor = moduleHover ? (alpha | 0x2A4A2A) : (alpha | 0x1A3A1A); // Green tint for enabled
            } else {
                moduleColor = moduleHover ? (alpha | 0x3A2A2A) : (alpha | 0x2A1A1A); // Red tint for disabled
            }

            int borderColor = module.isEnabled() ? (alpha | 0x4A7A4A) : (alpha | 0x7A4A4A);
            int textColor = alpha | 0xFFFFFF;

            RenderUtils.drawRoundedRect(context, 0, 0, RECT_WIDTH - 5, RECT_HEIGHT, CORNER_RADIUS - 1, moduleColor);
            RenderUtils.drawRoundedBorder(context, 0, 0, RECT_WIDTH - 5, RECT_HEIGHT, CORNER_RADIUS - 1, 1, borderColor);

            // Add glow effect for enabled modules
            if (module.isEnabled()) {
                context.fill(1, 1, RECT_WIDTH - 6, 2, alpha | 0x4A7A4A);
            }

            if (!anim.isSettled) {
                int outlineColor = alpha | 0x4A90E2;
                context.drawBorder(-1, -1, RECT_WIDTH - 3, RECT_HEIGHT + 2, outlineColor);
            }

            // Render module name
            context.drawTextWithShadow(this.textRenderer, module.getName(),
                5, (RECT_HEIGHT - 8) / 2, textColor);

            // Enhanced enabled/disabled indicator
            if (module.isEnabled()) {
                context.drawTextWithShadow(this.textRenderer, "●",
                        RECT_WIDTH - 5 - this.textRenderer.getWidth("●") - 5, (RECT_HEIGHT - 8) / 2, alpha | 0x00FF00);
            } else {
                context.drawTextWithShadow(this.textRenderer, "○",
                        RECT_WIDTH - 5 - this.textRenderer.getWidth("○") - 5, (RECT_HEIGHT - 8) / 2, alpha | 0xFF4444);
            }


            context.getMatrices().pop();
        }
    }

    private void renderSettingsPopup(DrawContext context) {
        if (settingsModule != null && currentSettingsAnimation != null && currentSettingsAnimation.alpha > 0.1f) {
            int popupWidth = 120;
            // Calculate dynamic height based on number of settings
            int popupHeight = 35 + settingsModule.getSettings().size() * 15;

            float animatedY = currentSettingsAnimation.currentY;
            float animatedAlpha = currentSettingsAnimation.alpha;
            float animatedScale = currentSettingsAnimation.scale;

            // Positioning relative to the module that opened it (settingsX, settingsY are module's TOP-LEFT)
            // The popup should appear below the module, so its Y will be moduleY + RECT_HEIGHT + RECT_MARGIN
            // Its X will be moduleX
            String categoryOfModule = findCategoryOfModule(settingsModule);
            int x = (categoryOfModule != null ? categoryX.get(categoryOfModule) : settingsX) + 5; // Get current X of module
            int y = (int)animatedY; // Use animated Y

            // Clamp to screen bounds
            x = Math.max(0, Math.min(this.width - popupWidth, x));
            y = Math.max(0, Math.min(this.height - popupHeight, y));


            context.getMatrices().push();
            context.getMatrices().translate(x + popupWidth / 2f, y + popupHeight / 2f, 0);
            context.getMatrices().scale(animatedScale, animatedScale, 1.0f);
            context.getMatrices().translate(-popupWidth / 2f, -popupHeight / 2f, 0);

            int popupAlpha = (int)(animatedAlpha * 0xEE) << 24;
            int popupColor = popupAlpha | 0x000000;
            int borderColor = (int)(animatedAlpha * 0xFF) << 24 | 0x666666;

            RenderUtils.drawRoundedRect(context, 0, 0, popupWidth, popupHeight, CORNER_RADIUS, popupColor);
            RenderUtils.drawRoundedBorder(context, 0, 0, popupWidth, popupHeight, CORNER_RADIUS, 1, borderColor);

            int textColor = (int)(animatedAlpha * 0xFF) << 24 | 0xFFFFFF;
            int settingValueColor = (int)(animatedAlpha * 0xFF) << 24 | 0x8888FF;

            context.drawTextWithShadow(this.textRenderer, settingsModule.getName() + " Settings",
                5, 5, textColor);

            int currentY = 20;
            for (Map.Entry<String, Object> entry : settingsModule.getSettings().entrySet()) {
                String settingName = entry.getKey();
                Object settingValue = entry.getValue();

                context.drawTextWithShadow(this.textRenderer, settingName + ":",
                    5, currentY, (int)(animatedAlpha * 0xFF) << 24 | 0xCCCCCC);

                String valueText;
                int valueColor = settingValueColor;

                if (settingValue instanceof Boolean) {
                    valueText = (Boolean) settingValue ? "[X]" : "[ ]";
                    valueColor = (Boolean) settingValue ? (textColor | 0x00FF00) : (textColor | 0xFF0000);
                } else if (settingValue instanceof Integer) {
                    valueText = String.valueOf((Integer) settingValue);
                } else if (settingValue instanceof Float) {
                    valueText = String.format("%.1f", (Float) settingValue); // Format floats
                } else {
                    valueText = String.valueOf(settingValue);
                }

                context.drawTextWithShadow(this.textRenderer, valueText,
                    popupWidth - 5 - this.textRenderer.getWidth(valueText), currentY, valueColor);
                currentY += 15;
            }
            context.drawTextWithShadow(this.textRenderer, "[Close]",
                5, popupHeight - 15, settingValueColor);

            context.getMatrices().pop();
        }
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Handle settings popup clicks first
        if (settingsModule != null && currentSettingsAnimation != null && currentSettingsAnimation.isSettled) {
            int popupWidth = 120;
            int popupHeight = 35 + settingsModule.getSettings().size() * 15;
            String categoryOfModule = findCategoryOfModule(settingsModule);
            int x = (categoryOfModule != null ? categoryX.get(categoryOfModule) : settingsX) + 5;
            int y = (int)currentSettingsAnimation.currentY; // Use animated Y for hit detection

            x = Math.max(0, Math.min(this.width - popupWidth, x));
            y = Math.max(0, Math.min(this.height - popupHeight, y));

            if (mouseX >= x && mouseX <= x + popupWidth && mouseY >= y && mouseY <= y + popupHeight) {
                // Click is within the popup
                int relativeMouseY = (int)(mouseY - y);

                // Check for close button
                if (relativeMouseY >= popupHeight - 15 && relativeMouseY <= popupHeight - 5 &&
                    mouseX >= x + 5 && mouseX <= x + 5 + textRenderer.getWidth("[Close]")) {
                    closeSettingsPopup();
                    return true;
                }

                int currentSettingYOffset = 20;
                for (Map.Entry<String, Object> entry : settingsModule.getSettings().entrySet()) {
                    String settingName = entry.getKey();
                    Object settingValue = entry.getValue();

                    // Check if click is on this setting line
                    if (relativeMouseY >= currentSettingYOffset && relativeMouseY <= currentSettingYOffset + 10) {
                        if (settingValue instanceof Boolean) {
                            settingsModule.getSettings().put(settingName, !(Boolean) settingValue);
                            // Also update the module's actual enabled state if this is the "Enabled" setting
                            if (settingName.equals("Enabled")) {
                                if ((Boolean) settingsModule.getSettings().get("Enabled")) {
                                    settingsModule.onEnable();
                                } else {
                                    settingsModule.onDisable();
                                }
                            }
                            return true;
                        } else if (settingValue instanceof Integer) {
                            int currentValue = (Integer) settingValue;
                            if (button == 0) { // Left click: increment
                                settingsModule.getSettings().put(settingName, currentValue + 1);
                            } else if (button == 1) { // Right click: decrement
                                settingsModule.getSettings().put(settingName, currentValue - 1);
                            }
                            return true;
                        } else if (settingValue instanceof Float) {
                            float currentValue = (Float) settingValue;
                            if (button == 0) { // Left click: increment
                                settingsModule.getSettings().put(settingName, currentValue + 0.1f);
                            } else if (button == 1) { // Right click: decrement
                                settingsModule.getSettings().put(settingName, currentValue - 0.1f);
                            }
                            return true;
                        }
                    }
                    currentSettingYOffset += 15;
                }
                return true; // Consumed click within popup
            } else {
                // Clicked outside settings popup, close it
                closeSettingsPopup();
                return true;
            }
        }

        // Category clicks
        for (String category : CATEGORIES.keySet()) {
            int startX = categoryX.get(category);
            int startY = categoryY.get(category);

            if (mouseX >= startX && mouseX <= startX + RECT_WIDTH &&
                mouseY >= startY && mouseY <= startY + RECT_HEIGHT) {

                if (button == 0) { // Left click - start dragging
                    isDragging = true;
                    draggedCategory = category;
                    dragStartX = mouseX;
                    dragStartY = mouseY;
                    dragStartCategoryX = startX;
                    dragStartCategoryY = startY;
                    closeSettingsPopup(); // Close settings if dragging a category
                    return true;
                } else if (button == 1) { // Right click - toggle expand/collapse
                    if (expandedCategories.contains(category)) {
                        expandedCategories.remove(category);
                        closeSettingsPopup(); // Close settings when collapsing a category
                    } else {
                        expandedCategories.add(category);
                        activateModuleAnimationsForCategory(category);
                    }
                    recalculateAllModulePositions(); // Recalculate all module positions after category change

                    // Save UI state immediately when categories are toggled
                    ConfigManager.saveUIState(expandedCategories, false);
                    return true;
                }
            }

            // Module clicks
            if (expandedCategories.contains(category)) {
                List<BaseModule> modules = CATEGORIES.get(category);
                for (BaseModule module : modules) {
                    ModuleAnimationState anim = allModuleAnimations.get(module);
                    if (anim != null && anim.isSettled) {
                        int moduleX = startX + 5; // Use category's current X for module hit detection
                        int moduleY = (int)anim.currentY;

                        if (mouseX >= moduleX && mouseX <= moduleX + RECT_WIDTH - 5 &&
                            mouseY >= moduleY && mouseY <= moduleY + RECT_HEIGHT) {

                            if (button == 0) { // Left click - toggle module
                                module.toggle(); // Use the module's toggle method
                                System.out.println(module.getName() + " is now " + (module.isEnabled() ? "ON" : "OFF"));
                                return true;
                            } else if (button == 1) { // Right click - open settings
                                if (settingsModule == module) {
                                    closeSettingsPopup(); // Toggle settings off if already open for this module
                                } else {
                                    openSettingsPopup(module, moduleX, moduleY + RECT_HEIGHT + RECT_MARGIN);
                                }
                                return true;
                            }
                        }
                    }
                }
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    private void openSettingsPopup(BaseModule module, int moduleRenderX, int moduleRenderY) {
        if (settingsModule != null && settingsModule != module) {
            // If another module's settings are open, close them first before opening new one
            closeSettingsPopup(); // This will trigger a close animation
        }

        settingsModule = module;
        // settingsX and settingsY are less critical now as popup position is dynamically calculated,
        // but can be used for initial animation start or fallback.
        settingsX = moduleRenderX;
        settingsY = moduleRenderY;

        // Calculate actual target Y for settings based on module's position and push other modules
        String category = findCategoryOfModule(settingsModule);
        if (category != null) {
            recalculateAllModulePositions(); // This will push down all modules in expanded categories
            ModuleAnimationState moduleAnim = allModuleAnimations.get(settingsModule);
            if (moduleAnim != null) {
                 float newSettingsTargetY = moduleAnim.targetY + RECT_HEIGHT + RECT_MARGIN; // Target directly below the module
                 currentSettingsAnimation = new SettingsAnimationState(moduleAnim.targetY, newSettingsTargetY); // Start from module's targetY for slide down
            }
        }
    }


    private void closeSettingsPopup() {
        settingsModule = null;
        // When settings close, restore all modules in expanded categories to visible state
        for (String category : expandedCategories) {
            List<BaseModule> modules = CATEGORIES.get(category);
            for (BaseModule module : modules) {
                ModuleAnimationState anim = allModuleAnimations.get(module);
                if (anim != null) {
                    anim.isSettled = true;
                    anim.velocityY = 0;
                    anim.scale = 1.0f;
                    anim.rotation = 0;
                    anim.alpha = 1.0f;
                    anim.hasStarted = true; // Ensure they're visible
                }
            }
        }
        currentSettingsAnimation = null;
        // Recalculate positions to restore proper layout
        recalculateAllModulePositions();
    }

    private String findCategoryOfModule(BaseModule targetModule) {
        for (Map.Entry<String, List<BaseModule>> entry : CATEGORIES.entrySet()) {
            if (entry.getValue().contains(targetModule)) {
                return entry.getKey();
            }
        }
        return null;
    }

    // Recalculates positions for modules in ALL expanded categories
    private void recalculateAllModulePositions() {
        for (String category : expandedCategories) {
            recalculateModulePositions(category);
        }
    }

    @Override
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        if (isDragging && draggedCategory != null && button == 0) {
            return true;
        }
        return super.mouseDragged(mouseX, mouseY, button, deltaX, deltaY);
    }

    @Override
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        if (isDragging && button == 0) {
            int newX = dragStartCategoryX + (int)(mouseX - dragStartX);
            int newY = dragStartCategoryY + (int)(mouseY - dragStartY);

            newX = Math.max(0, Math.min(this.width - RECT_WIDTH, newX));
            newY = Math.max(0, Math.min(this.height - RECT_HEIGHT, newY));

            categoryX.put(draggedCategory, newX);
            categoryY.put(draggedCategory, newY);

            // After dragging, recalculate module positions based on the final category position
            if (expandedCategories.contains(draggedCategory)) {
                recalculateModulePositions(draggedCategory);
            }

            isDragging = false;
            draggedCategory = null;
            return true;
        }
        return super.mouseReleased(mouseX, mouseY, button);
    }

    @Override
    public boolean shouldCloseOnEsc() {
        if (settingsModule != null) {
            closeSettingsPopup();
            return false; // Don't close the screen, just the popup
        }
        return true;
    }

    @Override
    public void close() {
        // Save all data before closing
        saveAllData();

        // Start closing animation
        if (!isClosing) {
            isClosing = true;
            isOpening = false;
            screenAnimStartTime = System.currentTimeMillis();
        }
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        if (keyCode == 256) { // ESC key
            close();
            return true;
        }
        return super.keyPressed(keyCode, scanCode, modifiers);
    }
}