package change.thisname.Modules;

import java.util.Map;

public class AimAssist extends BaseModule {
    public AimAssist() {
        super("AimAssist");
        addSetting("range", 4.2f);
        addSetting("speed", "Fast");
        addSetting("auto", true);
    }

    @Override
    public void onEnable() {
        System.out.println("AimAssist enabled");
    }

    @Override
    public void onDisable() {
        System.out.println("AimAssist disabled");
    }

    @Override
    public String getShortInfo() {
        return String.valueOf(settings.get("range"));
    }
}