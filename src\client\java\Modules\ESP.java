package change.thisname.Modules;

import net.fabricmc.fabric.api.client.rendering.v1.WorldRenderEvents;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.render.*;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.entity.Entity;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.mob.HostileEntity;
import net.minecraft.entity.passive.PassiveEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.Vec3d;
import org.joml.Matrix4f;

public class ESP extends BaseModule {
    private static ESP instance;

    public ESP() {
        super("ESP");
        instance = this;

        // Add settings
        addSetting("Players", true);
        addSetting("Mobs", true);
        addSetting("Animals", false);
        addSetting("Range", 64.0f);
        addSetting("Box", true);
        addSetting("Outline", false);
    }

    @Override
    public void onEnable() {
        System.out.println("ESP enabled");
        // Register the world render event
        WorldRenderEvents.AFTER_ENTITIES.register((context) -> {
            if (instance != null && instance.isEnabled()) {
                renderESP(context);
            }
        });
    }

    @Override
    public void onDisable() {
        System.out.println("ESP disabled");
        // The event will still be registered but won't render due to isEnabled() check
    }

    @Override
    public String getShortInfo() {
        return String.valueOf((int)((Float)settings.get("Range")));
    }

    private void renderESP(WorldRenderEvents.AfterEntities context) {
        MinecraftClient mc = MinecraftClient.getInstance();
        if (mc.player == null || mc.world == null) return;

        MatrixStack matrices = context.matrixStack();
        Camera camera = context.camera();
        Vec3d cameraPos = camera.getPos();

        boolean renderPlayers = (Boolean) settings.get("Players");
        boolean renderMobs = (Boolean) settings.get("Mobs");
        boolean renderAnimals = (Boolean) settings.get("Animals");
        float range = (Float) settings.get("Range");
        boolean renderBox = (Boolean) settings.get("Box");
        boolean renderOutline = (Boolean) settings.get("Outline");

        for (Entity entity : mc.world.getEntities()) {
            if (entity == mc.player) continue;
            if (entity.distanceTo(mc.player) > range) continue;

            boolean shouldRender = false;
            int color = 0xFFFFFFFF;

            if (entity instanceof PlayerEntity && renderPlayers) {
                shouldRender = true;
                color = 0xFF00FF00; // Green for players
            } else if (entity instanceof HostileEntity && renderMobs) {
                shouldRender = true;
                color = 0xFFFF0000; // Red for hostile mobs
            } else if (entity instanceof PassiveEntity && renderAnimals) {
                shouldRender = true;
                color = 0xFF0080FF; // Blue for animals
            }

            if (shouldRender) {
                matrices.push();

                Vec3d entityPos = entity.getPos();
                matrices.translate(
                    entityPos.x - cameraPos.x,
                    entityPos.y - cameraPos.y,
                    entityPos.z - cameraPos.z
                );

                if (renderBox) {
                    renderEntityBox(matrices, entity, color);
                }

                if (renderOutline) {
                    renderEntityOutline(matrices, entity, color);
                }

                matrices.pop();
            }
        }
    }

    private void renderEntityBox(MatrixStack matrices, Entity entity, int color) {
        Box box = entity.getBoundingBox().offset(-entity.getX(), -entity.getY(), -entity.getZ());

        float red = ((color >> 16) & 0xFF) / 255.0f;
        float green = ((color >> 8) & 0xFF) / 255.0f;
        float blue = (color & 0xFF) / 255.0f;
        float alpha = 0.3f;

        Tessellator tessellator = Tessellator.getInstance();
        BufferBuilder buffer = tessellator.getBuffer();

        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.disableCull();
        RenderSystem.setShader(GameRenderer::getPositionColorProgram);

        buffer.begin(VertexFormat.DrawMode.QUADS, VertexFormats.POSITION_COLOR);
        Matrix4f matrix = matrices.peek().getPositionMatrix();

        // Draw box faces
        drawBoxFaces(buffer, matrix, box, red, green, blue, alpha);

        tessellator.draw();

        RenderSystem.enableCull();
        RenderSystem.disableBlend();
    }

    private void renderEntityOutline(MatrixStack matrices, Entity entity, int color) {
        Box box = entity.getBoundingBox().offset(-entity.getX(), -entity.getY(), -entity.getZ());

        float red = ((color >> 16) & 0xFF) / 255.0f;
        float green = ((color >> 8) & 0xFF) / 255.0f;
        float blue = (color & 0xFF) / 255.0f;
        float alpha = 1.0f;

        Tessellator tessellator = Tessellator.getInstance();
        BufferBuilder buffer = tessellator.getBuffer();

        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.disableCull();
        RenderSystem.setShader(GameRenderer::getPositionColorProgram);
        RenderSystem.lineWidth(2.0f);

        buffer.begin(VertexFormat.DrawMode.DEBUG_LINES, VertexFormats.POSITION_COLOR);
        Matrix4f matrix = matrices.peek().getPositionMatrix();

        // Draw box edges
        drawBoxEdges(buffer, matrix, box, red, green, blue, alpha);

        tessellator.draw();

        RenderSystem.lineWidth(1.0f);
        RenderSystem.enableCull();
        RenderSystem.disableBlend();
    }

    private void drawBoxFaces(BufferBuilder buffer, Matrix4f matrix, Box box, float red, float green, float blue, float alpha) {
        float minX = (float) box.minX;
        float minY = (float) box.minY;
        float minZ = (float) box.minZ;
        float maxX = (float) box.maxX;
        float maxY = (float) box.maxY;
        float maxZ = (float) box.maxZ;

        // Bottom face
        buffer.vertex(matrix, minX, minY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, minY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, minY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, minY, maxZ).color(red, green, blue, alpha).next();

        // Top face
        buffer.vertex(matrix, minX, maxY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, maxY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, maxY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, maxY, minZ).color(red, green, blue, alpha).next();

        // North face
        buffer.vertex(matrix, minX, minY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, maxY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, maxY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, minY, minZ).color(red, green, blue, alpha).next();

        // South face
        buffer.vertex(matrix, maxX, minY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, maxY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, maxY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, minY, maxZ).color(red, green, blue, alpha).next();

        // West face
        buffer.vertex(matrix, minX, minY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, minY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, maxY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, maxY, minZ).color(red, green, blue, alpha).next();

        // East face
        buffer.vertex(matrix, maxX, minY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, minY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, maxY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, maxY, maxZ).color(red, green, blue, alpha).next();
    }

    private void drawBoxEdges(BufferBuilder buffer, Matrix4f matrix, Box box, float red, float green, float blue, float alpha) {
        float minX = (float) box.minX;
        float minY = (float) box.minY;
        float minZ = (float) box.minZ;
        float maxX = (float) box.maxX;
        float maxY = (float) box.maxY;
        float maxZ = (float) box.maxZ;

        // Bottom edges
        buffer.vertex(matrix, minX, minY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, minY, minZ).color(red, green, blue, alpha).next();

        buffer.vertex(matrix, maxX, minY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, minY, maxZ).color(red, green, blue, alpha).next();

        buffer.vertex(matrix, maxX, minY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, minY, maxZ).color(red, green, blue, alpha).next();

        buffer.vertex(matrix, minX, minY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, minY, minZ).color(red, green, blue, alpha).next();

        // Top edges
        buffer.vertex(matrix, minX, maxY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, maxY, minZ).color(red, green, blue, alpha).next();

        buffer.vertex(matrix, maxX, maxY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, maxY, maxZ).color(red, green, blue, alpha).next();

        buffer.vertex(matrix, maxX, maxY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, maxY, maxZ).color(red, green, blue, alpha).next();

        buffer.vertex(matrix, minX, maxY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, maxY, minZ).color(red, green, blue, alpha).next();

        // Vertical edges
        buffer.vertex(matrix, minX, minY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, maxY, minZ).color(red, green, blue, alpha).next();

        buffer.vertex(matrix, maxX, minY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, maxY, minZ).color(red, green, blue, alpha).next();

        buffer.vertex(matrix, maxX, minY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, maxY, maxZ).color(red, green, blue, alpha).next();

        buffer.vertex(matrix, minX, minY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, maxY, maxZ).color(red, green, blue, alpha).next();
    }
}