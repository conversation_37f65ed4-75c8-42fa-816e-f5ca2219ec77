package change.thisname;

import change.thisname.Render.GUI.ModuleScreen;

import change.thisname.Modules.BaseModule;
import java.util.List;
import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.rendering.v1.HudRenderCallback;
import net.minecraft.client.MinecraftClient;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import org.lwjgl.glfw.GLFW;

public class TemplateModClient implements ClientModInitializer {

	private static KeyBinding openGuiKeyBinding;

	@Override
	public void onInitializeClient() {
		// This entrypoint is suitable for setting up client-specific logic, such as rendering.

		openGuiKeyBinding = KeyBindingHelper.registerKeyBinding(new KeyBinding(
				"key.templatemod.opengui", // The translation key of the keybinding
				InputUtil.Type.KEYSYM, // The type of the keybinding, KEYSYM for keyboard keys
				GLFW.GLFW_KEY_RIGHT_SHIFT, // The keycode of the key
				"category.templatemod.gui" // The translation key of the keybinding category
		));

		ClientTickEvents.END_CLIENT_TICK.register(client -> {
			if (openGuiKeyBinding.isPressed()) {
				client.setScreen(new ModuleScreen(net.minecraft.text.Text.literal("Modules")));
			}
		});

		HudRenderCallback.EVENT.register((drawContext, tickDelta) -> {
			// Render HUD here
			int x = drawContext.getScaledWindowWidth() - 10; // Top right, adjust as needed
			int y = 10;
			int lineHeight = 9; // Approximate height of a line of text

			for (List<BaseModule> modules : ModuleScreen.CATEGORIES.values()) {
				modules.sort((m1, m2) -> Integer.compare(m2.getName().length(), m1.getName().length()));
				for (BaseModule module : modules) {
					if (module.isEnabled()) {
						String moduleText = module.getName();
						String shortInfo = module.getShortInfo();
						if (shortInfo != null && !shortInfo.isEmpty()) {
							moduleText += " [" + shortInfo + "]";
						}
						drawContext.drawTextWithShadow(MinecraftClient.getInstance().textRenderer, moduleText, x - MinecraftClient.getInstance().textRenderer.getWidth(moduleText), y, 0xFFFFFF);
						y += lineHeight;
					}
				}
			}
		});
	}
}