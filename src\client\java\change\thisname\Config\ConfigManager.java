package change.thisname.Config;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import change.thisname.Modules.BaseModule;
import change.thisname.Render.GUI.ModuleScreen;

import java.io.*;
import java.lang.reflect.Type;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

public class ConfigManager {
    private static final String CONFIG_DIR = "config/template-mod";
    private static final String POSITIONS_FILE = "category-positions.json";
    private static final String MODULES_FILE = "module-settings.json";
    private static final String UI_STATE_FILE = "ui-state.json";
    
    private static final Gson gson = new GsonBuilder().setPrettyPrinting().create();
    
    public static class CategoryPosition {
        public int x, y;
        
        public CategoryPosition(int x, int y) {
            this.x = x;
            this.y = y;
        }
    }
    
    public static class ModuleConfig {
        public String name;
        public boolean enabled;
        public Map<String, Object> settings;
        
        public ModuleConfig(String name, boolean enabled, Map<String, Object> settings) {
            this.name = name;
            this.enabled = enabled;
            this.settings = new HashMap<>(settings);
        }
    }
    
    public static class UIState {
        public Set<String> expandedCategories;
        public boolean firstLaunch;
        
        public UIState() {
            this.expandedCategories = new HashSet<>();
            this.firstLaunch = true;
        }
    }
    
    static {
        // Create config directory if it doesn't exist
        try {
            Path configPath = Paths.get(CONFIG_DIR);
            if (!Files.exists(configPath)) {
                Files.createDirectories(configPath);
            }
        } catch (IOException e) {
            System.err.println("Failed to create config directory: " + e.getMessage());
        }
    }
    
    public static void saveCategoryPositions(Map<String, Integer> categoryX, Map<String, Integer> categoryY) {
        try {
            Map<String, CategoryPosition> positions = new HashMap<>();
            for (String category : categoryX.keySet()) {
                positions.put(category, new CategoryPosition(categoryX.get(category), categoryY.get(category)));
            }
            
            String json = gson.toJson(positions);
            Files.write(Paths.get(CONFIG_DIR, POSITIONS_FILE), json.getBytes());
        } catch (IOException e) {
            System.err.println("Failed to save category positions: " + e.getMessage());
        }
    }
    
    public static Map<String, CategoryPosition> loadCategoryPositions() {
        try {
            Path file = Paths.get(CONFIG_DIR, POSITIONS_FILE);
            if (!Files.exists(file)) {
                return new HashMap<>();
            }
            
            String json = new String(Files.readAllBytes(file));
            Type type = new TypeToken<Map<String, CategoryPosition>>(){}.getType();
            return gson.fromJson(json, type);
        } catch (IOException e) {
            System.err.println("Failed to load category positions: " + e.getMessage());
            return new HashMap<>();
        }
    }
    
    public static void saveModuleConfigs() {
        try {
            List<ModuleConfig> configs = new ArrayList<>();
            
            for (List<BaseModule> modules : ModuleScreen.CATEGORIES.values()) {
                for (BaseModule module : modules) {
                    configs.add(new ModuleConfig(module.getName(), module.isEnabled(), module.getSettings()));
                }
            }
            
            String json = gson.toJson(configs);
            Files.write(Paths.get(CONFIG_DIR, MODULES_FILE), json.getBytes());
        } catch (IOException e) {
            System.err.println("Failed to save module configs: " + e.getMessage());
        }
    }
    
    public static void loadModuleConfigs() {
        try {
            Path file = Paths.get(CONFIG_DIR, MODULES_FILE);
            if (!Files.exists(file)) {
                return;
            }
            
            String json = new String(Files.readAllBytes(file));
            Type type = new TypeToken<List<ModuleConfig>>(){}.getType();
            List<ModuleConfig> configs = gson.fromJson(json, type);
            
            if (configs == null) return;
            
            // Apply loaded configs to modules
            for (ModuleConfig config : configs) {
                BaseModule module = findModuleByName(config.name);
                if (module != null) {
                    // Restore settings
                    if (config.settings != null) {
                        module.getSettings().putAll(config.settings);
                    }
                    
                    // Restore enabled state
                    if (config.enabled && !module.isEnabled()) {
                        module.toggle();
                    } else if (!config.enabled && module.isEnabled()) {
                        module.toggle();
                    }
                }
            }
        } catch (IOException e) {
            System.err.println("Failed to load module configs: " + e.getMessage());
        }
    }
    
    public static void saveUIState(Set<String> expandedCategories, boolean firstLaunch) {
        try {
            UIState state = new UIState();
            state.expandedCategories = new HashSet<>(expandedCategories);
            state.firstLaunch = firstLaunch;
            
            String json = gson.toJson(state);
            Files.write(Paths.get(CONFIG_DIR, UI_STATE_FILE), json.getBytes());
        } catch (IOException e) {
            System.err.println("Failed to save UI state: " + e.getMessage());
        }
    }
    
    public static UIState loadUIState() {
        try {
            Path file = Paths.get(CONFIG_DIR, UI_STATE_FILE);
            if (!Files.exists(file)) {
                return new UIState();
            }
            
            String json = new String(Files.readAllBytes(file));
            UIState state = gson.fromJson(json, UIState.class);
            return state != null ? state : new UIState();
        } catch (IOException e) {
            System.err.println("Failed to load UI state: " + e.getMessage());
            return new UIState();
        }
    }
    
    private static BaseModule findModuleByName(String name) {
        for (List<BaseModule> modules : ModuleScreen.CATEGORIES.values()) {
            for (BaseModule module : modules) {
                if (module.getName().equals(name)) {
                    return module;
                }
            }
        }
        return null;
    }
}
