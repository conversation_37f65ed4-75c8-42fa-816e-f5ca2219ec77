package change.thisname.Modules;

public class Timer extends BaseModule {
    public Timer() {
        super("Timer");
        addSetting("speed", "Slow");
    }

    @Override
    public void onEnable() {
        System.out.println("Timer enabled");
    }

    @Override
    public void onDisable() {
        System.out.println("Timer disabled");
    }

    @Override
    public String getShortInfo() {
        return String.valueOf(settings.get("speed"));
    }
}