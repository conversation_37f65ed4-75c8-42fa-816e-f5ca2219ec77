package change.thisname.Modules;

import java.util.HashMap;
import java.util.Map;

public abstract class BaseModule {
    protected String name;
    protected Map<String, Object> settings = new HashMap<>();

    public BaseModule(String name) {
        this.name = name;
        

    }

    public String getName() {
        return name;
    }

    public Map<String, Object> getSettings() {
        return settings;
    }

    public void addSetting(String key, Object value) {
        this.settings.put(key, value);
    }

    // Abstract method to be implemented by concrete modules for their specific logic
    protected boolean enabled = false;

    public abstract void onEnable();
    public abstract void onDisable();

    public boolean isEnabled() {
        return enabled;
    }

    public String getShortInfo() {
        return null;
    }

    public void toggle() {
        if (enabled) {
            onDisable();
            enabled = false;
        } else {
            onEnable();
            enabled = true;
        }
    }
}